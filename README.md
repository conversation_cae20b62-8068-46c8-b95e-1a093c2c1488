# Separador de Arquivos MP3

Este script Python encontra todos os arquivos MP3 em uma pasta (e suas subpastas) e os copia para uma pasta única.

## Funcionalidades

- 🔍 Busca recursiva por arquivos MP3 em todas as subpastas
- 📁 Cria automaticamente a pasta de destino se não existir
- 🔄 Evita conflitos de nomes adicionando numeração sequencial
- 📝 Gera log detalhado das operações
- ✅ Confirmação antes de iniciar a cópia
- 📊 Relatório final com estatísticas

## Como usar

1. **Execute o script:**
   ```bash
   python separar_mp3.py
   ```

2. **O script irá:**
   - Procurar todos os arquivos MP3 em `C:\Users\<USER>\Music`
   - Mostrar quantos arquivos foram encontrados
   - Pedir confirmação antes de copiar
   - Copiar todos os arquivos para `C:\Dev\python\Separar MP3\MP3_Reunidos`

## Configurações

Você pode alterar as pastas editando as variáveis no início da função `main()`:

```python
pasta_origem = r"C:\Users\<USER>\Music"
pasta_destino = r"C:\Dev\python\Separar MP3\MP3_Reunidos"
```

## Logs

O script gera um arquivo de log (`separar_mp3.log`) com todas as operações realizadas, incluindo:
- Arquivos encontrados
- Arquivos copiados com sucesso
- Erros encontrados
- Estatísticas finais

## Tratamento de Conflitos

Se dois arquivos tiverem o mesmo nome, o script automaticamente adiciona um número sequencial:
- `musica.mp3`
- `musica_1.mp3`
- `musica_2.mp3`

## Requisitos

- Python 3.6 ou superior
- Bibliotecas padrão do Python (não precisa instalar nada adicional)

## Segurança

- O script **copia** os arquivos (não move), mantendo os originais intactos
- Pede confirmação antes de iniciar a operação
- Gera logs detalhados para auditoria
