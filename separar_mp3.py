import os
import shutil
from pathlib import Path
import logging

def configurar_logging():
    """Configura o sistema de logging para acompanhar o progresso"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('separar_mp3.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

def criar_nome_unico(pasta_destino, nome_arquivo):
    """Cria um nome único para o arquivo caso já exista na pasta destino"""
    caminho_completo = pasta_destino / nome_arquivo
    
    if not caminho_completo.exists():
        return nome_arquivo
    
    # Se o arquivo já existe, adiciona um número sequencial
    nome_base = caminho_completo.stem
    extensao = caminho_completo.suffix
    contador = 1
    
    while True:
        novo_nome = f"{nome_base}_{contador}{extensao}"
        novo_caminho = pasta_destino / novo_nome
        
        if not novo_caminho.exists():
            return novo_nome
        
        contador += 1

def encontrar_mp3(pasta_origem):
    """Encontra todos os arquivos MP3 recursivamente na pasta origem"""
    pasta_origem = Path(pasta_origem)
    arquivos_mp3 = []
    
    if not pasta_origem.exists():
        logging.error(f"Pasta origem não encontrada: {pasta_origem}")
        return arquivos_mp3
    
    logging.info(f"Procurando arquivos MP3 em: {pasta_origem}")
    
    # Busca recursiva por arquivos .mp3
    for arquivo in pasta_origem.rglob("*.mp3"):
        if arquivo.is_file():
            arquivos_mp3.append(arquivo)
    
    # Também busca por .MP3 (maiúsculo)
    for arquivo in pasta_origem.rglob("*.MP3"):
        if arquivo.is_file():
            arquivos_mp3.append(arquivo)
    
    logging.info(f"Encontrados {len(arquivos_mp3)} arquivos MP3")
    return arquivos_mp3

def copiar_mp3(arquivos_mp3, pasta_destino):
    """Copia todos os arquivos MP3 para a pasta destino"""
    pasta_destino = Path(pasta_destino)
    
    # Cria a pasta destino se não existir
    pasta_destino.mkdir(parents=True, exist_ok=True)
    
    arquivos_copiados = 0
    arquivos_com_erro = 0
    
    for arquivo_origem in arquivos_mp3:
        try:
            # Cria um nome único para evitar conflitos
            nome_arquivo = criar_nome_unico(pasta_destino, arquivo_origem.name)
            arquivo_destino = pasta_destino / nome_arquivo
            
            # Copia o arquivo
            shutil.copy2(arquivo_origem, arquivo_destino)
            
            logging.info(f"Copiado: {arquivo_origem} -> {arquivo_destino}")
            arquivos_copiados += 1
            
        except Exception as e:
            logging.error(f"Erro ao copiar {arquivo_origem}: {str(e)}")
            arquivos_com_erro += 1
    
    return arquivos_copiados, arquivos_com_erro

def main():
    """Função principal do script"""
    configurar_logging()
    
    # Configurações
    pasta_origem = r"C:\Users\<USER>\Music"
    pasta_destino = r"C:\Dev\python\Separar MP3\MP3_Reunidos"
    
    logging.info("=== INICIANDO SEPARAÇÃO DE ARQUIVOS MP3 ===")
    logging.info(f"Pasta origem: {pasta_origem}")
    logging.info(f"Pasta destino: {pasta_destino}")
    
    # Encontra todos os arquivos MP3
    arquivos_mp3 = encontrar_mp3(pasta_origem)
    
    if not arquivos_mp3:
        logging.warning("Nenhum arquivo MP3 encontrado!")
        return
    
    # Mostra estatísticas antes de copiar
    logging.info(f"Total de arquivos MP3 encontrados: {len(arquivos_mp3)}")
    
    # Pergunta confirmação ao usuário
    resposta = input(f"\nDeseja copiar {len(arquivos_mp3)} arquivos MP3 para '{pasta_destino}'? (s/n): ")
    
    if resposta.lower() not in ['s', 'sim', 'y', 'yes']:
        logging.info("Operação cancelada pelo usuário")
        return
    
    # Copia os arquivos
    logging.info("Iniciando cópia dos arquivos...")
    arquivos_copiados, arquivos_com_erro = copiar_mp3(arquivos_mp3, pasta_destino)
    
    # Relatório final
    logging.info("=== RELATÓRIO FINAL ===")
    logging.info(f"Arquivos encontrados: {len(arquivos_mp3)}")
    logging.info(f"Arquivos copiados com sucesso: {arquivos_copiados}")
    logging.info(f"Arquivos com erro: {arquivos_com_erro}")
    logging.info(f"Pasta destino: {pasta_destino}")
    
    if arquivos_com_erro == 0:
        logging.info("✅ Todos os arquivos foram copiados com sucesso!")
    else:
        logging.warning(f"⚠️ {arquivos_com_erro} arquivos tiveram problemas na cópia")

if __name__ == "__main__":
    main()
